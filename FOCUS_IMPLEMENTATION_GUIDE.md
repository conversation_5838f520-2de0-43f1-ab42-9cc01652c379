# Focus Preservation Implementation Guide

## Overview

This document outlines the focus preservation implementation for the search input in the file list. The infrastructure is complete and ready for the cosmic framework's specific focus API.

## Current Implementation Status

✅ **Complete Infrastructure**
- Unique widget ID system (`search_input_id`)
- Focus state tracking (`should_focus_search`)
- Message handling (`FocusSearchInput`)
- Background filtering with focus restoration trigger

✅ **Working Features**
- Background filtering without UI blocking
- Immediate filtering response (no debouncing for maximum responsiveness)
- Optimized filtering (path + tags only, no redundant filename filtering)
- Visual feedback with "Filtering..." indicator in header
- Race condition prevention

🔄 **Needs Investigation**
- Cosmic framework's specific focus API

## Code Structure

### Key Components

1. **Widget ID Management**
```rust
pub search_input_id: cosmic::iced::widget::text_input::Id,
```

2. **Focus State Tracking**
```rust
pub should_focus_search: bool,
```

3. **Message Flow**
```
SearchChanged → FilteringComplete → FocusSearchInput → try_focus_search_input()
```

4. **Focus Restoration Trigger**
```rust
// In FilteringComplete handler:
self.should_focus_search = true;
cosmic::task::message(FileListMessage::FocusSearchInput)
```

## Focus API Investigation Points

### Approach 1: Direct Iced Integration
```rust
// Try in try_focus_search_input():
cosmic::iced::widget::text_input::focus(self.search_input_id.clone())
```

### Approach 2: Cosmic Task System
```rust
// Try in try_focus_search_input():
cosmic::task::widget(cosmic::iced::widget::text_input::focus(self.search_input_id.clone()))
```

### Approach 3: Cosmic Action System
```rust
// Try in try_focus_search_input():
Task::done(cosmic::Action::Focus(self.search_input_id.clone()))
```

### Approach 4: Cosmic Command System
```rust
// Try in try_focus_search_input():
cosmic::command::focus(self.search_input_id.clone())
```

### Approach 5: View-Level Focus
```rust
// Try in view() method:
if self.should_focus_search {
    // Set focus during view rendering
}
```

## Investigation Steps

1. **Check Cosmic Documentation**
   - Look for focus management examples
   - Check cosmic's widget command system
   - Review cosmic's action system variants

2. **Examine Cosmic Source Code**
   - Look at `cosmic::Action` enum variants
   - Check if `cosmic::task` has widget-related functions
   - See how other cosmic apps handle focus

3. **Test Standard Iced Approaches**
   - Try `cosmic::iced::widget::text_input::focus()` directly
   - Check if cosmic re-exports iced's focus functions

4. **Community Resources**
   - Check cosmic framework discussions
   - Look at other cosmic applications
   - Ask in cosmic development channels

## Files Modified

- `cosmic/src/page/file_list.rs` - Main implementation
- `cosmic/i18n/en/archive_organizer_cosmic.ftl` - Localization

## Testing the Implementation

1. **Current Behavior**
   - Search input loses focus after filtering completes
   - All other functionality works correctly

2. **Expected Behavior After Fix**
   - Search input maintains focus during filtering
   - User can continue typing seamlessly

3. **Test Cases**
   - Type quickly in search box
   - Verify focus is maintained after filtering
   - Test with large file lists
   - Test clear search button

## Next Steps

1. Replace `cosmic::task::none()` in `try_focus_search_input()` with working focus API
2. Test the implementation
3. Refine based on cosmic framework's specific requirements

The infrastructure is complete - only the cosmic-specific focus API call needs to be determined and implemented.
