// SPDX-License-Identifier: GPL-3.0-or-later

use crate::app::ContextView;
use crate::client::{Client, ClientSelector};
use crate::fl;
use crate::state::LoadedState;
use archive_organizer::api::{File, FileDataSource};
use cosmic::iced::Length;
use cosmic::iced::alignment::{Horizontal, Vertical};
use cosmic::iced_widget::list::Content;
use cosmic::widget;
use cosmic::{Apply, Element, Task};
use std::path::Path;

type FileState = LoadedState<Content<File>>;

fn view_file<'a>(file: &'a File) -> Element<'a, FileListMessage> {
    display_path(&file.path)
        .apply(cosmic::iced_widget::button)
        .on_press(FileListMessage::Out(FileListOutput::OpenFileDetails(
            file.clone(),
        )))
        .into()
}

fn display_path<'a>(path: &'a str) -> Element<'a, FileListMessage> {
    let path: &Path = path.as_ref();
    let directory = format!("{}", path.parent().unwrap().display());
    let filename = path.file_name().unwrap();
    cosmic::iced_widget::column![
        widget::text(format!("{}", filename.to_string_lossy())),
        widget::text(directory).size(11),
    ]
    .spacing(5)
    .apply(widget::container)
    .width(Length::Fill)
    .into()
}

impl FileState {
    pub fn view(&self) -> Element<FileListMessage> {
        match self {
            FileState::New => widget::text(fl!("file-list-new")).into(), // TODO: Show spinner
            FileState::Loading => widget::text(fl!("file-list-loading")).into(), // TODO: Show spinner
            FileState::Failed(error) => {
                widget::text(fl!("generic-error", error = error.as_str())).into()
            }
            FileState::Loaded(files) => {
                let list =
                    cosmic::iced::widget::list(files, |_index, file| view_file(file)).spacing(10);
                list.apply(widget::scrollable::vertical).into()
            }
        }
    }
}

pub struct FileList {
    pub client: Client,
    pub archive: FileState,
    pub search_query: String,
    pub all_files: Vec<File>, // Store all files for filtering
}

#[derive(Debug, Clone)]
pub enum FileListOutput {
    OpenFileDetails(File),
    ToggleContextPage(ClientSelector),
}

#[derive(Debug, Clone)]
pub enum FileListMessage {
    LoadArchive,
    Loaded(Vec<File>),
    LoadingFailed(String),
    SearchChanged(String),
    ClearSearch,
    Out(FileListOutput),
}

impl FileList {
    /// Filter files based on the search query
    fn filter_files(&self) -> Vec<File> {
        if self.search_query.is_empty() {
            self.all_files.clone()
        } else {
            let query = self.search_query.to_lowercase();
            self.all_files
                .iter()
                .filter(|file| {
                    // Search in file path, filename, and tags
                    let path_lower = file.path.to_lowercase();
                    let filename_lower = Path::new(&file.path)
                        .file_name()
                        .and_then(|name| name.to_str())
                        .unwrap_or("")
                        .to_lowercase();
                    let tags_lower = file.tags.join(" ").to_lowercase();

                    path_lower.contains(&query)
                        || filename_lower.contains(&query)
                        || tags_lower.contains(&query)
                })
                .cloned()
                .collect()
        }
    }

    pub fn new(client: Client) -> (Self, Task<cosmic::Action<FileListMessage>>) {
        (
            Self {
                client,
                archive: FileState::default(),
                search_query: String::new(),
                all_files: Vec::new(),
            },
            cosmic::task::message(FileListMessage::LoadArchive),
        )
    }

    pub fn display_name(&self) -> String {
        self.client.display_name()
    }

    pub fn view(&self) -> Element<FileListMessage> {
        let column = widget::column();

        let header_row = widget::row();

        let header_row = header_row.push(
            widget::button::icon(widget::icon::from_name("open-menu-symbolic"))
                .on_press(FileListMessage::Out(FileListOutput::ToggleContextPage(
                    self.client.selector(),
                )))
                .apply(widget::container)
                .width(Length::Shrink)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let header_row = header_row.push(
            cosmic::iced_widget::text_input("Search files...", &self.search_query)
                .on_input(FileListMessage::SearchChanged)
                .width(Length::FillPortion(2))
                .apply(widget::container)
                .width(Length::FillPortion(2))
                .height(Length::Shrink)
                .align_x(Horizontal::Left)
                .align_y(Vertical::Center),
        );

        let header_row = header_row.push(
            widget::button::icon(widget::icon::from_name("edit-clear-symbolic"))
                .on_press(FileListMessage::ClearSearch)
                .apply(widget::container)
                .width(Length::Shrink)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let header_row = header_row.push(
            widget::text(self.client.display_name())
                .apply(widget::container)
                .width(Length::FillPortion(1))
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let column = column.push(
            header_row
                .apply(widget::container)
                .width(Length::Fill)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let column = column.push(
            self.archive
                .view()
                .apply(widget::container)
                .width(Length::Fill)
                .height(Length::Fill)
                .align_x(Horizontal::Left)
                .align_y(Vertical::Top),
        );

        column.into()
    }

    pub fn view_context(&self) -> ContextView<FileListMessage> {
        ContextView {
            title: "File List".to_string(),
            content: widget::text("TODO").into(),
        }
    }

    pub fn update(&mut self, message: FileListMessage) -> Task<cosmic::Action<FileListMessage>> {
        tracing::debug!("received: {message:?}");
        match message {
            FileListMessage::LoadArchive => {
                self.archive = FileState::Loading;
                let client = self.client.clone();
                cosmic::task::future(async move {
                    match client.get_files().await {
                        Ok(files) => FileListMessage::Loaded(files),
                        Err(error) => FileListMessage::LoadingFailed(format!("{error}")),
                    }
                })
            }
            FileListMessage::Loaded(files) => {
                self.all_files = files;
                let filtered_files = self.filter_files();
                self.archive = FileState::Loaded(Content::with_items(filtered_files));
                cosmic::task::none()
            }
            FileListMessage::LoadingFailed(error) => {
                self.archive = FileState::Failed(error);
                cosmic::task::none()
            }
            FileListMessage::SearchChanged(query) => {
                self.search_query = query;
                // Only update the archive if files have been loaded
                if !self.all_files.is_empty() {
                    let filtered_files = self.filter_files();
                    self.archive = FileState::Loaded(Content::with_items(filtered_files));
                }
                cosmic::task::none()
            }
            FileListMessage::ClearSearch => {
                self.search_query.clear();
                // Only update the archive if files have been loaded
                if !self.all_files.is_empty() {
                    let filtered_files = self.filter_files();
                    self.archive = FileState::Loaded(Content::with_items(filtered_files));
                }
                cosmic::task::none()
            }
            FileListMessage::Out(_) => {
                panic!("should be handled by the parent component")
            }
        }
    }
}
