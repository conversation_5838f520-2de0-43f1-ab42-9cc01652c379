// SPDX-License-Identifier: GPL-3.0-or-later

use crate::app::ContextView;
use crate::client::{Client, ClientSelector};
use crate::fl;
use crate::state::LoadedState;
use archive_organizer::api::{File, FileDataSource};
use cosmic::iced::Length;
use cosmic::iced::alignment::{Horizontal, Vertical};
use cosmic::iced_widget::list::Content;
use cosmic::widget;
use cosmic::{Apply, Element, Task};
use std::path::Path;

type FileState = LoadedState<Content<File>>;

fn view_file<'a>(file: &'a File) -> Element<'a, FileListMessage> {
    display_path(&file.path)
        .apply(cosmic::iced_widget::button)
        .on_press(FileListMessage::Out(FileListOutput::OpenFileDetails(
            file.clone(),
        )))
        .into()
}

fn display_path<'a>(path: &'a str) -> Element<'a, FileListMessage> {
    let path: &Path = path.as_ref();
    let directory = format!("{}", path.parent().unwrap().display());
    let filename = path.file_name().unwrap();
    cosmic::iced_widget::column![
        widget::text(format!("{}", filename.to_string_lossy())),
        widget::text(directory).size(11),
    ]
    .spacing(5)
    .apply(widget::container)
    .width(Length::Fill)
    .into()
}

impl FileState {
    pub fn view(&self) -> Element<FileListMessage> {
        match self {
            FileState::New => widget::text(fl!("file-list-new")).into(), // TODO: Show spinner
            FileState::Loading => widget::text(fl!("file-list-loading")).into(), // TODO: Show spinner
            FileState::Failed(error) => {
                widget::text(fl!("generic-error", error = error.as_str())).into()
            }
            FileState::Loaded(files) => {
                let list =
                    cosmic::iced::widget::list(files, |_index, file| view_file(file)).spacing(10);
                list.apply(widget::scrollable::vertical).into()
            }
        }
    }
}

pub struct FileList {
    pub client: Client,
    pub archive: FileState,
    pub all_files: Vec<File>,                // Store all files for filtering
    pub is_filtering: bool,                  // Track if filtering is in progress
    pub search_query: String,                // The search query string
    pub search_input_id: cosmic::widget::Id, // Unique ID for focus management
    pub search_input_is_focussed: bool,      // Flag to indicate search input should be focused
}

#[derive(Debug, Clone)]
pub enum FileListOutput {
    OpenFileDetails(File),
    ToggleContextPage(ClientSelector),
}

#[derive(Debug, Clone)]
pub enum FileListMessage {
    LoadArchive,
    Loaded(Vec<File>),
    LoadingFailed(String),
    SearchChanged(String),
    ClearSearch,
    FilteringComplete(Vec<File>),
    FocusSearchInput,
    Out(FileListOutput),
}

impl FileList {
    /// Filter files based on the search query (synchronous version for immediate use)
    fn filter_files_sync(&self) -> Vec<File> {
        if self.search_query.is_empty() {
            self.all_files.clone()
        } else {
            let query = self.search_query.to_lowercase();
            self.all_files
                .iter()
                .filter(|file| {
                    // Search in file path, filename, and tags
                    let path_lower = file.path.to_lowercase();
                    let filename_lower = Path::new(&file.path)
                        .file_name()
                        .and_then(|name| name.to_str())
                        .unwrap_or("")
                        .to_lowercase();
                    let tags_lower = file.tags.join(" ").to_lowercase();

                    path_lower.contains(&query)
                        || filename_lower.contains(&query)
                        || tags_lower.contains(&query)
                })
                .cloned()
                .collect()
        }
    }

    /// Attempt to focus the search input using various cosmic framework approaches
    /// This method contains multiple approaches that could work depending on cosmic's API
    fn try_focus_search_input(&self) -> Task<cosmic::Action<FileListMessage>> {
        cosmic::widget::text_input::focus(self.search_input_id.clone())
    }

    /// Start background filtering task with debouncing
    fn start_background_filtering(
        &self,
        query: String,
        all_files: Vec<File>,
    ) -> Task<cosmic::Action<FileListMessage>> {
        cosmic::task::future(async move {
            // Add a small delay to debounce rapid typing
            tokio::time::sleep(tokio::time::Duration::from_millis(150)).await;

            // Perform filtering in background
            let filtered_files = if query.is_empty() {
                all_files
            } else {
                let query_lower = query.to_lowercase();
                all_files
                    .into_iter()
                    .filter(|file| {
                        // Search in file path, filename, and tags
                        let path_lower = file.path.to_lowercase();
                        let filename_lower = Path::new(&file.path)
                            .file_name()
                            .and_then(|name| name.to_str())
                            .unwrap_or("")
                            .to_lowercase();
                        let tags_lower = file.tags.join(" ").to_lowercase();

                        path_lower.contains(&query_lower)
                            || filename_lower.contains(&query_lower)
                            || tags_lower.contains(&query_lower)
                    })
                    .collect()
            };

            FileListMessage::FilteringComplete(filtered_files)
        })
    }

    pub fn new(client: Client) -> (Self, Task<cosmic::Action<FileListMessage>>) {
        (
            Self {
                client,
                archive: FileState::default(),
                search_query: String::new(),
                all_files: Vec::new(),
                is_filtering: false,
                search_input_id: cosmic::widget::Id::unique(),
                search_input_is_focussed: false,
            },
            cosmic::task::batch(vec![
                cosmic::task::message(FileListMessage::LoadArchive),
                cosmic::task::message(FileListMessage::FocusSearchInput),
            ]),
        )
    }

    pub fn display_name(&self) -> String {
        self.client.display_name()
    }

    pub fn view(&self) -> Element<FileListMessage> {
        let column = widget::column().spacing(10);

        let header_row = widget::row().align_y(Vertical::Center);

        let header_row = header_row.push(
            widget::button::icon(widget::icon::from_name("open-menu-symbolic"))
                .on_press(FileListMessage::Out(FileListOutput::ToggleContextPage(
                    self.client.selector(),
                )))
                .apply(widget::container)
                .width(Length::Shrink)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let search_input =
            cosmic::widget::text_input(fl!("file-list-search-placeholder"), &self.search_query)
                .id(self.search_input_id.clone())
                .always_active()
                .on_input(FileListMessage::SearchChanged)
                .width(Length::FillPortion(2));

        let header_row = header_row.push(
            search_input
                .apply(widget::container)
                .height(Length::Shrink)
                .align_x(Horizontal::Left)
                .align_y(Vertical::Center),
        );

        let header_row = header_row.push(
            widget::button::icon(widget::icon::from_name("edit-clear-symbolic"))
                .on_press(FileListMessage::ClearSearch)
                .apply(widget::container)
                .width(Length::Shrink)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let header_row = if self.is_filtering {
            // Show filtering indicator in the header
            header_row.push(
                widget::text(fl!("file-list-filtering"))
                    .size(12)
                    .apply(widget::container)
                    .width(Length::Shrink)
                    .height(Length::Shrink)
                    .align_x(Horizontal::Center)
                    .align_y(Vertical::Center),
            )
        } else {
            header_row
        };

        let header_row = header_row.push(
            widget::text(self.client.display_name())
                .apply(widget::container)
                .width(Length::FillPortion(1))
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let column = column.push(
            header_row
                .apply(widget::container)
                .width(Length::Fill)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let column = column.push(
            self.archive
                .view()
                .apply(widget::container)
                .width(Length::Fill)
                .height(Length::Fill)
                .align_x(Horizontal::Left)
                .align_y(Vertical::Top),
        );

        column.into()
    }

    pub fn view_context(&self) -> ContextView<FileListMessage> {
        ContextView {
            title: "File List".to_string(),
            content: widget::text("TODO").into(),
        }
    }

    pub fn update(&mut self, message: FileListMessage) -> Task<cosmic::Action<FileListMessage>> {
        tracing::debug!("received: {message:?}");
        match message {
            FileListMessage::LoadArchive => {
                self.archive = FileState::Loading;
                let client = self.client.clone();
                cosmic::task::future(async move {
                    match client.get_files().await {
                        Ok(files) => FileListMessage::Loaded(files),
                        Err(error) => FileListMessage::LoadingFailed(format!("{error}")),
                    }
                })
            }
            FileListMessage::Loaded(files) => {
                self.all_files = files.clone();
                // For initial load, use synchronous filtering since it's typically fast
                let filtered_files = self.filter_files_sync();
                self.archive = FileState::Loaded(Content::with_items(filtered_files));
                cosmic::task::none()
            }
            FileListMessage::LoadingFailed(error) => {
                self.archive = FileState::Failed(error);
                cosmic::task::none()
            }
            FileListMessage::SearchChanged(query) => {
                self.search_query = query.clone();
                // Only start filtering if files have been loaded and we're not already filtering
                if !self.all_files.is_empty() && !self.is_filtering {
                    self.is_filtering = true;
                    self.start_background_filtering(query, self.all_files.clone())
                } else {
                    cosmic::task::none()
                }
            }
            FileListMessage::ClearSearch => {
                self.search_query.clear();
                // Only start filtering if files have been loaded and we're not already filtering
                if !self.all_files.is_empty() && !self.is_filtering {
                    self.is_filtering = true;
                    self.start_background_filtering(String::new(), self.all_files.clone())
                } else {
                    cosmic::task::none()
                }
            }
            FileListMessage::FilteringComplete(filtered_files) => {
                self.is_filtering = false;
                self.archive = FileState::Loaded(Content::with_items(filtered_files));
                // Set flag to focus search input after re-render
                self.search_input_is_focussed = true;
                cosmic::task::message(FileListMessage::FocusSearchInput)
            }

            FileListMessage::FocusSearchInput => {
                self.search_input_is_focussed = false;
                // Use the helper method that contains all the focus approaches to try
                self.try_focus_search_input()
            }
            FileListMessage::Out(_) => {
                panic!("should be handled by the parent component")
            }
        }
    }
}
